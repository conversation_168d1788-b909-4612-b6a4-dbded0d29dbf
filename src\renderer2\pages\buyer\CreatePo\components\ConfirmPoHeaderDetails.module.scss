.dialogContent {
  width: 652px;
  background: #2a2d35;
  border-radius: 12px;
  padding: 0;
  color: #fff;
  font-family: 'Inter', sans-serif;
  position: relative;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px 20px 32px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.title {
  font-family: 'Syncopate', sans-serif;
  font-size: 16px;
  font-weight: bold;
  letter-spacing: 0.56px;
  color: #fff;
  margin: 0;
}

.closeButton {
  display: flex;
  align-items: center;
  gap: 8px;
  color: rgba(255, 255, 255, 0.6);
  padding: 4px 8px;
  
  &:hover {
    color: #fff;
    background: rgba(255, 255, 255, 0.1);
  }
  
  svg {
    font-size: 16px;
  }
}

.cancelText {
  font-size: 14px;
  font-weight: 400;
  letter-spacing: 0.4px;
}

.formContent {
  padding: 32px;
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.formRow {
  display: flex;
  gap: 32px;
  
  &:last-child {
    .fieldGroup {
      flex: 1;
    }
  }
}

.fieldGroup {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.fieldLabel {
  font-size: 12px;
  font-weight: 600;
  letter-spacing: 0.8px;
  color: rgba(255, 255, 255, 0.6);
  text-transform: uppercase;
  margin: 0;
}

.fieldValue {
  background: rgba(255, 255, 255, 0.04);
  border: 1px solid transparent;
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 16px;
  font-weight: 400;
  color: #fff;
  min-height: 20px;
  
  &:empty::before {
    content: attr(data-placeholder);
    color: rgba(255, 255, 255, 0.4);
  }
}

.deliveryAddress {
  background: rgba(255, 255, 255, 0.04);
  border: 1px solid transparent;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.addressLine {
  font-size: 16px;
  font-weight: 400;
  color: #fff;
  line-height: 1.4;
}

.cityStateZip {
  display: flex;
  gap: 16px;
  align-items: center;
  
  .city, .state, .zip {
    font-size: 16px;
    font-weight: 400;
    color: #fff;
  }
  
  .state {
    min-width: 32px;
  }
  
  .zip {
    min-width: 60px;
  }
}

.footer {
  padding: 24px 32px 32px 32px;
  display: flex;
  justify-content: flex-end;
}

.saveButton {
  background: transparent;
  border: 1px solid #4cff06;
  border-radius: 4px;
  color: #fff;
  font-family: 'Syncopate', sans-serif;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: 0.48px;
  padding: 12px 32px;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: #32ff6c;
    color: #0f0f14;
    border-color: #32ff6c;
  }
  
  &:focus {
    outline: none;
    background: #32ff6c;
    color: #0f0f14;
    border-color: #32ff6c;
  }
}
