import React from 'react';
import { IconButton } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import clsx from 'clsx';
import styles from './ConfirmPoHeaderDetails.module.scss';

interface ConfirmPoHeaderDetailsProps {
    onClose: () => void;
    onSave: () => void;
    headerData: {
        jobPo?: string;
        deliverBy?: string;
        deliverTo?: {
            line1?: string;
            line2?: string;
            city?: string;
            state?: string;
            zip?: string;
        };
    };
}

const ConfirmPoHeaderDetails: React.FC<ConfirmPoHeaderDetailsProps> = ({
    onClose,
    onSave,
    headerData
}) => {
    const formatDeliveryAddress = () => {
        const { deliverTo } = headerData;
        if (!deliverTo) return '';
        
        const addressParts = [];
        if (deliverTo.line1) addressParts.push(deliverTo.line1);
        if (deliverTo.line2) addressParts.push(deliverTo.line2);
        
        return addressParts.join(', ');
    };

    return (
        <div className={styles.dialogContent}>
            <div className={styles.header}>
                <h2 className={styles.title}>CONFIRM PO HEADER DETAILS</h2>
                <IconButton
                    onClick={onClose}
                    className={styles.closeButton}
                    size="small"
                >
                    <CloseIcon />
                    <span className={styles.cancelText}>Cancel</span>
                </IconButton>
            </div>

            <div className={styles.formContent}>
                    <div className={styles.formRow}>
                        <div className={styles.fieldGroup}>
                            <label className={styles.fieldLabel}>JOB / PO#</label>
                            <div className={styles.fieldValue}>
                                {headerData.jobPo || 'Railings'}
                            </div>
                        </div>
                        <div className={styles.fieldGroup}>
                            <label className={styles.fieldLabel}>DELIVER BY</label>
                            <div className={styles.fieldValue}>
                                {headerData.deliverBy || ''}
                            </div>
                        </div>
                    </div>

                    <div className={styles.formRow}>
                        <div className={styles.fieldGroup}>
                            <label className={styles.fieldLabel}>DELIVER TO</label>
                            <div className={styles.deliveryAddress}>
                                <div className={styles.addressLine}>
                                    {formatDeliveryAddress() || '123 Main St'}
                                </div>
                                <div className={styles.cityStateZip}>
                                    <span className={styles.city}>
                                        {headerData.deliverTo?.city || 'Chicago'}
                                    </span>
                                    <span className={styles.state}>
                                        {headerData.deliverTo?.state || 'IL'}
                                    </span>
                                    <span className={styles.zip}>
                                        {headerData.deliverTo?.zip || '60606'}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div className={styles.footer}>
                    <button
                        className={styles.saveButton}
                        onClick={onSave}
                    >
                        SAVE
                    </button>
                </div>
            </div>
    );
};

export default ConfirmPoHeaderDetails;
